# Firebase Setup Guide for KaleidoneX

This guide will help you set up Firebase for the KaleidoneX template marketplace application.

## Step 1: Create a Firebase Project

1. Go to the [Firebase Console](https://console.firebase.google.com)
2. Click "Create a project" or "Add project"
3. Enter project name: `kaleidonex` (or your preferred name)
4. Choose whether to enable Google Analytics (optional)
5. Click "Create project"

## Step 2: Enable Authentication

1. In your Firebase project, go to **Authentication** in the left sidebar
2. Click on the **Sign-in method** tab
3. Enable **Email/Password** authentication:
   - Click on "Email/Password"
   - Toggle "Enable" to ON
   - Click "Save"

## Step 3: Create Firestore Database

1. Go to **Firestore Database** in the left sidebar
2. Click "Create database"
3. Choose "Start in test mode" (we'll set up security rules later)
4. Select a location for your database (choose closest to your users)
5. Click "Done"

## Step 4: Get Firebase Configuration

1. Go to **Project Settings** (gear icon in the left sidebar)
2. Scroll down to "Your apps" section
3. Click on the web icon `</>`
4. Register your app with a nickname (e.g., "KaleidoneX Web")
5. Copy the Firebase configuration object

Your configuration should look like this:
```javascript
const firebaseConfig = {
  apiKey: "your-api-key",
  authDomain: "your-project.firebaseapp.com",
  projectId: "your-project-id",
  storageBucket: "your-project.appspot.com",
  messagingSenderId: "123456789",
  appId: "your-app-id"
};
```

## Step 5: Update Environment Variables

1. Copy the values from your Firebase config
2. Update your `.env.local` file with the actual values:

```env
NEXT_PUBLIC_FIREBASE_API_KEY=your-actual-api-key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your-project-id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your-project.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your-sender-id
NEXT_PUBLIC_FIREBASE_APP_ID=your-app-id
```

## Step 6: Set Up Firestore Security Rules

1. Go to **Firestore Database** > **Rules**
2. Replace the default rules with the following:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can read/write their own data
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Templates are readable by all, writable by admins
    match /templates/{templateId} {
      allow read: if true;
      allow write: if request.auth != null && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }
    
    // Orders are readable by owner and admins, writable by admins
    match /orders/{orderId} {
      allow read: if request.auth != null && 
        (resource.data.userId == request.auth.uid || 
         get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin');
      allow write: if request.auth != null && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }
    
    // Custom requests are readable by owner and admins
    match /customRequests/{requestId} {
      allow read, create: if request.auth != null;
      allow update: if request.auth != null && 
        (resource.data.userId == request.auth.uid || 
         get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin');
    }
    
    // Categories are readable by all, writable by admins
    match /categories/{categoryId} {
      allow read: if true;
      allow write: if request.auth != null && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }
  }
}
```

3. Click "Publish"

## Step 7: Create Initial Data (Optional)

You can add some initial categories to your Firestore database:

1. Go to **Firestore Database** > **Data**
2. Click "Start collection"
3. Collection ID: `categories`
4. Add documents with the following structure:

```json
{
  "name": "Dashboard",
  "description": "Admin panels and data visualization templates",
  "imageUrl": "",
  "templateCount": 0
}
```

Repeat for other categories: E-commerce, Portfolio, Landing Page, Corporate, Mobile App, etc.

## Step 8: Create Admin User

1. Sign up for an account in your application
2. Go to **Firestore Database** > **Data**
3. Find the `users` collection and your user document
4. Edit the document and change the `role` field from `"user"` to `"admin"`

## Step 9: Test the Application

1. Start your development server: `npm run dev`
2. Open http://localhost:3000
3. Try signing up and signing in
4. Test admin functionality if you created an admin user

## Troubleshooting

### Common Issues:

1. **"Firebase: Error (auth/invalid-api-key)"**
   - Check that your API key is correct in `.env.local`
   - Make sure there are no extra spaces or quotes

2. **"Missing or insufficient permissions"**
   - Check your Firestore security rules
   - Make sure the user has the correct role

3. **"Firebase app not initialized"**
   - Check that all environment variables are set correctly
   - Restart your development server after changing `.env.local`

### Environment Variables Checklist:
- [ ] `NEXT_PUBLIC_FIREBASE_API_KEY` is set
- [ ] `NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN` is set
- [ ] `NEXT_PUBLIC_FIREBASE_PROJECT_ID` is set
- [ ] `NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET` is set
- [ ] `NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID` is set
- [ ] `NEXT_PUBLIC_FIREBASE_APP_ID` is set

## Next Steps

Once Firebase is set up:
1. Test user registration and login
2. Create some sample templates (as admin)
3. Test the order flow
4. Customize the application for your needs

For more help, check the [Firebase Documentation](https://firebase.google.com/docs) or contact support.
