'use client';

import React from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';

export const HeroSection = () => {
  return (
    <section className="bg-white py-12 sm:py-16 lg:py-24">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto text-center">
          {/* Hero Content */}
          <div className="mb-8 sm:mb-12">
            <h1 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 mb-4 sm:mb-6 leading-tight">
              Premium Templates for
              <span className="text-blue-600">
                {" "}Your Business
              </span>
            </h1>
            <p className="text-base sm:text-lg text-gray-600 mb-6 sm:mb-8 max-w-2xl mx-auto">
              Professional, ready-to-use templates that help you build beautiful websites
              and applications quickly and efficiently.
            </p>
          </div>

          {/* Demo Video */}
          <div className="mb-8 sm:mb-12">
            <div className="relative w-full max-w-2xl mx-auto h-64 sm:h-80 bg-gray-100 rounded-lg overflow-hidden shadow-lg">
              <video
                className="w-full h-full object-cover"
                autoPlay
                loop
                muted
                playsInline
              >
                <source src="../../public/videos/test.mp4" type="video/mp4" />
                <source src="/videos/template-demo.webm" type="video/webm" />
                {/* Fallback content */}
                <div className="flex items-center justify-center h-full bg-gradient-to-r from-blue-50 to-purple-50">
                  <div className="text-center">
                    <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <svg className="w-8 h-8 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <p className="text-sm text-gray-600 font-medium">Template Showcase</p>
                    <p className="text-xs text-gray-500">Video preview coming soon</p>
                  </div>
                </div>
              </video>
            </div>
            <p className="text-xs sm:text-sm text-gray-500 text-center mt-3">
              See our premium templates in action
            </p>
          </div>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center mb-12 sm:mb-16">
            <Button asChild size="lg" className="px-6 sm:px-8 py-3">
              <Link href="/templates">
                Browse Templates
              </Link>
            </Button>
            <Button asChild variant="outline" size="lg" className="px-6 sm:px-8 py-3">
              <Link href="/contact">
                Get Started
              </Link>
            </Button>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 sm:gap-8 max-w-3xl mx-auto">
            {/* <div className="text-center">
              <div className="text-2xl sm:text-3xl font-bold text-gray-900 mb-2">500+</div>
              <div className="text-sm sm:text-base text-gray-600">Premium Templates</div>
            </div> */}
            {/* <div className="text-center">
              <div className="text-2xl sm:text-3xl font-bold text-gray-900 mb-2">10,000+</div>
              <div className="text-sm sm:text-base text-gray-600">Happy Customers</div>
            </div> */}
            {/* <div className="text-center">
              <div className="text-2xl sm:text-3xl font-bold text-gray-900 mb-2">24/7</div>
              <div className="text-sm sm:text-base text-gray-600">Support</div>
            </div> */}
          </div>
        </div>
      </div>
    </section>
  );
};
