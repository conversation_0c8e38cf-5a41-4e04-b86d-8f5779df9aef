'use client';

import React from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';

export const HeroSection = () => {
  return (
    <section className="bg-white py-12 sm:py-16 lg:py-24">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Mobile Layout - Centered */}
        <div className="lg:hidden max-w-4xl mx-auto text-center">
          {/* Brand Name */}
          <div className="mb-6">
            <h2 className="text-2xl sm:text-3xl font-bold text-blue-600 mb-2">KaleidoneX</h2>
          </div>

          {/* Hero Content */}
          <div className="mb-8 sm:mb-12">
            <h1 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4 sm:mb-6 leading-tight">
              Premium Templates for
              <span className="text-blue-600">
                {" "}Your Business
              </span>
            </h1>
            <p className="text-base sm:text-lg text-gray-600 mb-6 sm:mb-8 max-w-2xl mx-auto">
              Professional, ready-to-use templates that help you build beautiful websites
              and applications quickly and efficiently.
            </p>
          </div>

          {/* Demo Video */}
          <div className="mb-8 sm:mb-12">
            <div className="relative w-full max-w-2xl mx-auto h-48 sm:h-64 md:h-80 bg-gray-100 rounded-lg overflow-hidden shadow-lg">
              <video
                className="w-full h-full object-cover"
                autoPlay
                loop
                muted
                playsInline
              >
                <source src="/videos/test.mp4" type="video/mp4" />
                <source src="/videos/template-demo.webm" type="video/webm" />
                {/* Fallback content */}
                <div className="flex items-center justify-center h-full bg-gradient-to-r from-blue-50 to-purple-50">
                  <div className="text-center">
                    <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <svg className="w-8 h-8 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <p className="text-sm text-gray-600 font-medium">Template Showcase</p>
                    <p className="text-xs text-gray-500">Video preview coming soon</p>
                  </div>
                </div>
              </video>
            </div>
            <p className="text-xs sm:text-sm text-gray-500 text-center mt-3">
              See our premium templates in action
            </p>
          </div>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center">
            <Button asChild size="lg" className="px-6 sm:px-8 py-3">
              <Link href="/templates">
                Browse Templates
              </Link>
            </Button>
            <Button asChild variant="outline" size="lg" className="px-6 sm:px-8 py-3">
              <Link href="/contact">
                Get Started
              </Link>
            </Button>
          </div>
        </div>

        {/* Desktop Layout - Side by Side */}
        <div className="hidden lg:block">
          <div className="grid lg:grid-cols-2 gap-12 items-center max-w-7xl mx-auto">
            {/* Left Content */}
            <div className="space-y-8">
              {/* Brand Name */}
              <div>
                <h2 className="text-4xl xl:text-5xl font-bold text-blue-600 mb-4">KaleidoneX</h2>
              </div>

              {/* Hero Content */}
              <div>
                <h1 className="text-4xl xl:text-5xl 2xl:text-6xl font-bold text-gray-900 mb-6 leading-tight">
                  Premium Templates for
                  <span className="text-blue-600">
                    {" "}Your Business
                  </span>
                </h1>
                <p className="text-lg xl:text-xl text-gray-600 mb-8 leading-relaxed">
                  Professional, ready-to-use templates that help you build beautiful websites
                  and applications quickly and efficiently.
                </p>
              </div>

              {/* CTA Buttons */}
              <div className="flex gap-4">
                <Button asChild size="lg" className="px-8 py-3 text-lg">
                  <Link href="/templates">
                    Browse Templates
                  </Link>
                </Button>
                <Button asChild variant="outline" size="lg" className="px-8 py-3 text-lg">
                  <Link href="/contact">
                    Get Started
                  </Link>
                </Button>
              </div>
            </div>

            {/* Right Video */}
            <div className="relative">
              <div className="relative w-full h-80 xl:h-96 2xl:h-[500px] bg-gray-100 rounded-lg overflow-hidden shadow-2xl">
                <video
                  className="w-full h-full object-cover"
                  autoPlay
                  loop
                  muted
                  playsInline
                >
                  <source src="/videos/test.mp4" type="video/mp4" />
                  <source src="/videos/template-demo.webm" type="video/webm" />
                  {/* Fallback content */}
                  <div className="flex items-center justify-center h-full bg-gradient-to-r from-blue-50 to-purple-50">
                    <div className="text-center">
                      <div className="w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg className="w-10 h-10 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <p className="text-lg text-gray-600 font-medium">Template Showcase</p>
                      <p className="text-sm text-gray-500">Video preview coming soon</p>
                    </div>
                  </div>
                </video>
              </div>
              <p className="text-sm text-gray-500 text-center mt-4">
                See our premium templates in action
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};
