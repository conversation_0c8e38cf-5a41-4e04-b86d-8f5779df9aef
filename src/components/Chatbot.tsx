'use client';

import React, { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { MessageCircle, X, Send, Bot, User, ChevronDown } from 'lucide-react';
import { getChatbotQAs, ChatbotQA } from '@/lib/firebaseServices';
import { toast } from 'sonner';
import Image from 'next/image';

interface Message {
  id: string;
  text: string;
  isBot: boolean;
  timestamp: Date;
  options?: string[];
}

interface ChatbotProps {
  customIcon?: string;
}

export const Chatbot: React.FC<ChatbotProps> = ({ customIcon }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [messages, setMessages] = useState<Message[]>([]);
  const [qas, setQAs] = useState<ChatbotQA[]>([]);
  const [loading, setLoading] = useState(false);
  const [showOptions, setShowOptions] = useState(true);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    fetchQAs();
  }, []);

  useEffect(() => {
    if (isOpen && messages.length === 0) {
      // Initial greeting message
      const welcomeMessage: Message = {
        id: '1',
        text: "👋 Hi! I'm here to help you with any questions about our templates and services. What would you like to know?",
        isBot: true,
        timestamp: new Date(),
        options: qas.map(qa => qa.question)
      };
      setMessages([welcomeMessage]);
      setShowOptions(true);
    }
  }, [isOpen, qas]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const fetchQAs = async () => {
    try {
      setLoading(true);
      const fetchedQAs = await getChatbotQAs();
      setQAs(fetchedQAs);
    } catch (error) {
      console.error('Error fetching chatbot QAs:', error);
      // Fallback to default QAs
      setQAs([
        {
          id: '1',
          question: 'What types of templates do you offer?',
          answer: 'We offer a wide variety of templates including Dashboard, E-commerce, Landing Pages, Portfolio, Education, Blog, Business, and Technology templates. All our templates are professionally designed and fully responsive.',
          category: 'General',
          order: 1,
          active: true,
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          id: '2',
          question: 'How much do templates cost?',
          answer: 'Our template prices range from ₹1,999 to ₹4,999 depending on the complexity and features. We also offer custom pricing for bulk purchases and enterprise solutions.',
          category: 'Pricing',
          order: 2,
          active: true,
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          id: '3',
          question: 'Do you provide customization services?',
          answer: 'Yes! We provide full customization services for all our templates. You can request custom modifications, color schemes, layouts, and additional features. Contact us for a personalized quote.',
          category: 'Services',
          order: 3,
          active: true,
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          id: '4',
          question: 'What technologies are used in your templates?',
          answer: 'Our templates are built with modern technologies including React, Next.js, TypeScript, Tailwind CSS, and other cutting-edge frameworks. All templates are optimized for performance and SEO.',
          category: 'Technical',
          order: 4,
          active: true,
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          id: '5',
          question: 'How do I purchase and download templates?',
          answer: 'To purchase a template, click "Contact to Buy" on any template card. We\'ll contact you with payment details and delivery instructions. After payment, you\'ll receive the complete template files and documentation.',
          category: 'Purchase',
          order: 5,
          active: true,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ]);
    } finally {
      setLoading(false);
    }
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleQuestionClick = (question: string) => {
    // Add user message
    const userMessage: Message = {
      id: Date.now().toString(),
      text: question,
      isBot: false,
      timestamp: new Date()
    };

    // Find the answer
    const qa = qas.find(q => q.question === question);
    const botMessage: Message = {
      id: (Date.now() + 1).toString(),
      text: qa?.answer || "I'm sorry, I don't have an answer for that question right now. Please contact our support team for assistance.",
      isBot: true,
      timestamp: new Date(),
      options: qas.map(q => q.question).filter(q => q !== question)
    };

    setMessages(prev => [...prev, userMessage, botMessage]);
    setShowOptions(true);
  };

  const handleReset = () => {
    setMessages([]);
    setShowOptions(true);
  };

  if (!isOpen) {
    return (
      <div className="fixed bottom-8 right-6 z-50">
        <Button
          onClick={() => setIsOpen(true)}
          className="w-16 h-16 rounded-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 shadow-lg hover:shadow-xl transition-all duration-300 group"
        >
          {customIcon ? (
            <div className="w-8 h-8 relative">
              <Image
                src={customIcon}
                alt="Chat"
                fill
                className="object-cover rounded-full group-hover:scale-110 transition-transform"
              />
            </div>
          ) : (
            <MessageCircle className="h-7 w-7 text-white group-hover:scale-110 transition-transform" />
          )}
        </Button>
      </div>
    );
  }

  return (
    <div className="fixed bottom-8 right-6 z-50">
      <Card className="w-80 sm:w-96 h-[500px] shadow-2xl border-0 overflow-hidden">
        <CardHeader className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
                {customIcon ? (
                  <div className="w-6 h-6 relative">
                    <Image
                      src={customIcon}
                      alt="Chat"
                      fill
                      className="object-cover rounded-full"
                    />
                  </div>
                ) : (
                  <Bot className="h-5 w-5" />
                )}
              </div>
              <div>
                <CardTitle className="text-sm font-semibold">AI Assistant</CardTitle>
                <p className="text-xs text-white/80">Online • Ready to help</p>
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsOpen(false)}
              className="text-white hover:bg-white/20 p-1 h-auto"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>

        <CardContent className="p-0 h-full flex flex-col">
          {/* Messages Area */}
          <div className="flex-1 overflow-y-auto p-4 space-y-3 bg-gray-50">
            {messages.map((message) => (
              <div key={message.id} className={`flex ${message.isBot ? 'justify-start' : 'justify-end'}`}>
                <div className={`flex items-start space-x-2 max-w-[80%] ${message.isBot ? '' : 'flex-row-reverse space-x-reverse'}`}>
                  <div className={`w-6 h-6 rounded-full flex items-center justify-center flex-shrink-0 ${
                    message.isBot ? 'bg-blue-100 text-blue-600' : 'bg-purple-100 text-purple-600'
                  }`}>
                    {message.isBot ? <Bot className="h-3 w-3" /> : <User className="h-3 w-3" />}
                  </div>
                  <div className={`rounded-lg p-3 ${
                    message.isBot 
                      ? 'bg-white border shadow-sm' 
                      : 'bg-gradient-to-r from-blue-600 to-purple-600 text-white'
                  }`}>
                    <p className="text-sm leading-relaxed">{message.text}</p>
                    <p className={`text-xs mt-1 ${message.isBot ? 'text-gray-500' : 'text-white/70'}`}>
                      {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                    </p>
                  </div>
                </div>
              </div>
            ))}

            {/* Quick Options */}
            {showOptions && messages.length > 0 && (
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                    <Bot className="h-3 w-3 text-blue-600" />
                  </div>
                  <p className="text-xs text-gray-600">Choose a question:</p>
                </div>
                <div className="space-y-1 ml-8">
                  {qas.slice(0, 3).map((qa) => (
                    <Button
                      key={qa.id}
                      variant="outline"
                      size="sm"
                      onClick={() => handleQuestionClick(qa.question)}
                      className="w-full text-left justify-start text-xs h-auto py-2 px-3 hover:bg-blue-50 hover:border-blue-200"
                    >
                      {qa.question}
                    </Button>
                  ))}
                  {qas.length > 3 && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setShowOptions(false)}
                      className="w-full text-xs text-gray-500 hover:text-gray-700"
                    >
                      <ChevronDown className="h-3 w-3 mr-1" />
                      Show more options
                    </Button>
                  )}
                </div>
              </div>
            )}

            <div ref={messagesEndRef} />
          </div>

          {/* Footer */}
          <div className="p-3 border-t bg-white">
            <div className="flex items-center justify-between">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleReset}
                className="text-xs text-gray-500 hover:text-gray-700"
              >
                Start Over
              </Button>
              <Badge variant="secondary" className="text-xs">
                Powered by AI
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default Chatbot;
