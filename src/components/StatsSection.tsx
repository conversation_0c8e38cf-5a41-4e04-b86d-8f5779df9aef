'use client';

import React from 'react';
import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { 
  Download, 
  Star, 
  Users, 
  Globe,
  ArrowRight,
  CheckCircle 
} from 'lucide-react';

const stats = [
  {
    icon: Download,
    value: '50,000+',
    label: 'Total Downloads',
    description: 'Templates downloaded by developers worldwide'
  },
  {
    icon: Star,
    value: '4.9/5',
    label: 'Average Rating',
    description: 'Based on 10,000+ customer reviews'
  },
  {
    icon: Users,
    value: '15,000+',
    label: 'Happy Customers',
    description: 'Satisfied developers and designers'
  },
  {
    icon: Globe,
    value: '120+',
    label: 'Countries',
    description: 'Global reach across all continents'
  }
];

const features = [
  'Premium quality designs',
  'Regular updates and support',
  'Mobile-responsive layouts',
  'Clean, modern code',
  'Comprehensive documentation',
  'Lifetime access'
];

export const StatsSection = () => {
  return (
    <section className="py-12 sm:py-16 bg-blue-600 text-white">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          <h2 className="text-2xl sm:text-3xl font-bold mb-4">
            Ready to Get Started?
          </h2>
          <p className="text-base sm:text-lg lg:text-xl text-blue-100 mb-6 sm:mb-8 max-w-2xl mx-auto">
            Join thousands of businesses using our professional templates to build amazing websites.
          </p>

          <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center">
            <Button asChild size="lg" className="bg-white text-blue-600 hover:bg-blue-50 px-6 sm:px-8">
              <Link href="/templates">
                Browse Templates
              </Link>
            </Button>
            <Button asChild variant="outline" size="lg" className="border-white text-white hover:bg-white/10 px-6 sm:px-8">
              <Link href="/contact">
                Contact Us
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
};
