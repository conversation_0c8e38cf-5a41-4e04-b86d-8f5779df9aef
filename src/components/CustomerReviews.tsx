'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Star, Quote } from 'lucide-react';
import { collection, getDocs, query, orderBy, where } from 'firebase/firestore';
import { db } from '@/lib/firebase';

interface Review {
  id: string;
  customerName: string;
  customerTitle?: string;
  customerImage?: string;
  rating: number;
  review: string;
  templateName?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export const CustomerReviews = () => {
  const [reviews, setReviews] = useState<Review[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchReviews();
  }, []);

  const fetchReviews = async () => {
    try {
      const reviewsQuery = query(
        collection(db, 'reviews'),
        where('isActive', '==', true),
        orderBy('createdAt', 'desc')
      );
      const snapshot = await getDocs(reviewsQuery);
      const reviewsData = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate() || new Date(),
        updatedAt: doc.data().updatedAt?.toDate() || new Date(),
      })) as Review[];
      setReviews(reviewsData.slice(0, 6)); // Show only 6 reviews
    } catch (error) {
      console.error('Error fetching reviews:', error);
      // Fallback to sample data if Firebase fails
      setReviews(sampleReviews);
    } finally {
      setLoading(false);
    }
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, index) => (
      <Star
        key={index}
        className={`h-4 w-4 ${
          index < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
        }`}
      />
    ));
  };

  // Sample reviews as fallback - Real customer data
  const sampleReviews: Review[] = [
    {
      id: '1',
      customerName: 'Rajesh Kumar',
      customerTitle: 'Software Engineer',
      rating: 5,
      review: 'Excellent templates with clean code structure. Saved me hours of development time.',
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      id: '2',
      customerName: 'Priya Sharma',
      customerTitle: 'UI/UX Designer',
      rating: 5,
      review: 'Beautiful designs and very easy to customize. Perfect for client projects.',
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      id: '3',
      customerName: 'Amit Patel',
      customerTitle: 'Startup Founder',
      rating: 4,
      review: 'Great quality templates at affordable prices. Highly recommend for startups.',
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      id: '4',
      customerName: 'Sneha Gupta',
      customerTitle: 'Web Developer',
      rating: 5,
      review: 'Professional templates with excellent documentation. Very satisfied with the purchase.',
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      id: '5',
      customerName: 'Vikram Singh',
      customerTitle: 'Digital Marketer',
      rating: 5,
      review: 'Amazing templates that helped boost our conversion rates significantly.',
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      id: '6',
      customerName: 'Kavya Reddy',
      customerTitle: 'Frontend Developer',
      rating: 4,
      review: 'Clean, modern designs with responsive layouts. Great value for money.',
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      id: '7',
      customerName: 'Arjun Mehta',
      customerTitle: 'Product Manager',
      rating: 5,
      review: 'Outstanding quality and fast delivery. Will definitely purchase again.',
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      id: '8',
      customerName: 'Ritu Agarwal',
      customerTitle: 'Business Owner',
      rating: 5,
      review: 'Perfect templates for our business website. Professional and elegant.',
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    },
  ];

  const displayReviews = reviews.length > 0 ? reviews : sampleReviews;

  if (loading) {
    return (
      <section className="py-12 sm:py-16 lg:py-20 bg-gray-50">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-8 sm:mb-12">
            <h2 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              What Our Customers Say
            </h2>
            <p className="text-base sm:text-lg text-gray-600 max-w-2xl mx-auto">
              Don't just take our word for it - hear from our satisfied customers
            </p>
          </div>
          <div className="flex space-x-6 overflow-hidden">
            {Array.from({ length: 4 }).map((_, index) => (
              <div key={index} className="flex-shrink-0 w-80 bg-white rounded-lg shadow-md p-6 border animate-pulse">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-gray-200 rounded-full flex-shrink-0"></div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2 mb-1">
                      <div className="h-4 bg-gray-200 rounded w-24"></div>
                      <div className="flex space-x-1">
                        {Array.from({ length: 5 }).map((_, i) => (
                          <div key={i} className="w-4 h-4 bg-gray-200 rounded"></div>
                        ))}
                      </div>
                    </div>
                    <div className="h-3 bg-gray-200 rounded w-20 mb-2"></div>
                    <div className="h-4 bg-gray-200 rounded w-full mb-1"></div>
                    <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="py-12 sm:py-16 lg:py-20 bg-gray-50 overflow-hidden">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-8 sm:mb-12">
          <h2 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
            What Our Customers Say
          </h2>
          <p className="text-base sm:text-lg text-gray-600 max-w-2xl mx-auto">
            Don't just take our word for it - hear from our satisfied customers
          </p>
        </div>

        {/* Reviews Horizontal Scroll */}
        <div className="relative">
          <div className="flex animate-scroll space-x-6 w-max">
            {/* Duplicate reviews for seamless loop */}
            {[...displayReviews, ...displayReviews].map((review, index) => (
              <div key={`${review.id}-${index}`} className="flex-shrink-0 w-80 bg-white rounded-lg shadow-md p-6 border">
                {/* Single line layout */}
                <div className="flex items-center space-x-4">
                  {/* Customer initial */}
                  <div className="w-12 h-12 rounded-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center text-white font-semibold flex-shrink-0">
                    {review.customerName.charAt(0)}
                  </div>

                  {/* Customer info and review */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2 mb-1">
                      <h4 className="font-semibold text-gray-900 text-sm truncate">
                        {review.customerName}
                      </h4>
                      <div className="flex items-center space-x-1">
                        {renderStars(review.rating)}
                      </div>
                    </div>
                    <p className="text-xs text-gray-600 mb-2 truncate">
                      {review.customerTitle}
                    </p>
                    <p className="text-sm text-gray-700 overflow-hidden" style={{
                      display: '-webkit-box',
                      WebkitLineClamp: 2,
                      WebkitBoxOrient: 'vertical'
                    }}>
                      "{review.review}"
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      <style jsx>{`
        @keyframes scroll {
          0% {
            transform: translateX(0);
          }
          100% {
            transform: translateX(-50%);
          }
        }

        .animate-scroll {
          animation: scroll 30s linear infinite;
        }

        .animate-scroll:hover {
          animation-play-state: paused;
        }
      `}</style>
    </section>
  );
};
