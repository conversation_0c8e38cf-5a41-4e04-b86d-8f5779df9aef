"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Search, Eye, Star, Heart, Zap, Award, Filter, MessageCircle } from "lucide-react"
import { collection, getDocs, query, orderBy } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { Template } from '@/types';
import { toast } from "sonner"
import { useRouter, usePathname } from "next/navigation"
import { useAuth } from '@/contexts/AuthContext';
import { createContactMessage, toggleFavorite } from '@/lib/firebaseServices';

export default function TemplatesPage() {
  const [templates, setTemplates] = useState<Template[]>([])
  const [filteredTemplates, setFilteredTemplates] = useState<Template[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("All")
  const [sortBy, setSortBy] = useState("title")
  const [categories, setCategories] = useState<string[]>(["All"])
  const [userFavorites, setUserFavorites] = useState<string[]>([])

  const router = useRouter()
  const pathname = usePathname()
  const { user, userData, refreshUserData } = useAuth()

  useEffect(() => {
    const handlePageShow = (event: PageTransitionEvent) => {
      if (event.persisted) {
        console.log('Page restored from bfcache, refetching templates...')
        fetchTemplates()
      }
    };

    fetchTemplates()
    window.addEventListener('pageshow', handlePageShow);

    return () => {
      window.removeEventListener('pageshow', handlePageShow);
    };
  }, [pathname])

  useEffect(() => {
    filterAndSortTemplates()
  }, [templates, searchTerm, selectedCategory, sortBy])

  // Update local favorites when userData changes
  useEffect(() => {
    if (userData?.favoriteTemplates) {
      setUserFavorites(userData.favoriteTemplates);
    } else {
      setUserFavorites([]);
    }
  }, [userData])

  const fetchTemplates = async () => {
    try {
      const templatesQuery = query(collection(db, 'templates'), orderBy('createdAt', 'desc'));
      const querySnapshot = await getDocs(templatesQuery);
      const templatesData = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate() || new Date(),
        updatedAt: doc.data().updatedAt?.toDate() || new Date(),
      })) as Template[];

      setTemplates(templatesData);

      // Extract unique categories
      const uniqueCategories = ["All", ...new Set(templatesData?.map(t => t.category) || [])]
      setCategories(uniqueCategories)
    } catch (error) {
      console.error('Error fetching templates:', error)
      toast.error('Failed to load templates')
      
      // Fallback to mock data
      const mockTemplates = [
        {
          id: '1',
          title: 'Creative Portfolio',
          description: 'Showcase your creative work with this stunning portfolio template',
          category: 'Portfolio',
          price: 1999,
          imageUrl: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=300&fit=crop',
          rating: 4.9,
          downloads: 1234,
          featured: true,
          tags: ['Responsive', 'Modern', 'Fast'],
          createdAt: new Date(),
          updatedAt: new Date(),
          createdBy: 'admin'
        },
        {
          id: '2',
          title: 'E-commerce Store',
          description: 'Complete e-commerce solution with shopping cart and payment integration',
          category: 'E-commerce',
          price: 4999,
          imageUrl: 'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=400&h=300&fit=crop',
          rating: 4.9,
          downloads: 1234,
          featured: true,
          tags: ['Responsive', 'Modern', 'Fast'],
          createdAt: new Date(),
          updatedAt: new Date(),
          createdBy: 'admin'
        },
        {
          id: '3',
          title: 'Education Platform',
          description: 'Complete education platform template with course management',
          category: 'Education',
          price: 3499,
          imageUrl: 'https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=400&h=300&fit=crop',
          rating: 4.9,
          downloads: 1234,
          featured: true,
          tags: ['Responsive', 'Modern', 'Fast'],
          createdAt: new Date(),
          updatedAt: new Date(),
          createdBy: 'admin'
        }
      ] as Template[];
      
      setTemplates(mockTemplates);
      setCategories(["All", "Portfolio", "E-commerce", "Education"]);
    } finally {
      setLoading(false)
    }
  }

  const filterAndSortTemplates = () => {
    let filtered = templates

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(template =>
        template.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        template.description?.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    // Filter by category
    if (selectedCategory !== "All") {
      filtered = filtered.filter(template => template.category === selectedCategory)
    }

    // Sort templates
    filtered.sort((a, b) => {
      switch (sortBy) {
        case "price-low":
          return a.price - b.price
        case "price-high":
          return b.price - a.price
        case "title":
        default:
          return a.title.localeCompare(b.title)
      }
    })

    setFilteredTemplates(filtered)
  }

  const handleContactRequest = async (template: Template) => {
    if (!user) {
      toast.error('Please sign in to contact us')
      router.push('/auth')
      return
    }

    // Check if user has completed profile (mobile number required)
    if (!userData?.phoneNumber || !userData?.fullName) {
      toast.error('Please complete your profile with mobile number before contacting us')
      router.push('/profile')
      return
    }

    try {
      await createContactMessage({
        userId: user.uid,
        userEmail: user.email!,
        userName: userData.fullName,
        userPhone: `${userData.countryCode || '+1'} ${userData.phoneNumber}`,
        subject: `Inquiry about ${template.title}`,
        message: `Hi, I'm interested in the ${template.title} template. Could you please provide more information about customization options and pricing?`,
        type: 'contact',
        templateId: template.id,
        templateTitle: template.title,
        status: 'pending'
      })

      toast.success('Contact request sent! We\'ll get back to you soon.')
    } catch (error) {
      console.error('Error sending contact request:', error)
      toast.error('Failed to send contact request. Please try again.')
    }
  }

  const handleBuyRequest = async (template: Template) => {
    if (!user) {
      toast.error('Please sign in to make a purchase request')
      router.push('/auth')
      return
    }

    // Check if user has completed profile (mobile number required)
    if (!userData?.phoneNumber || !userData?.fullName) {
      toast.error('Please complete your profile with mobile number before making a purchase request')
      router.push('/profile')
      return
    }

    try {
      await createContactMessage({
        userId: user.uid,
        userEmail: user.email!,
        userName: userData.fullName,
        userPhone: `${userData.countryCode || '+1'} ${userData.phoneNumber}`,
        subject: `Purchase Request for ${template.title}`,
        message: `Hi, I would like to purchase the ${template.title} template. Please provide payment instructions and delivery details.`,
        type: 'purchase-request',
        templateId: template.id,
        templateTitle: template.title,
        status: 'pending'
      })

      toast.success('Purchase request sent! We\'ll contact you with payment details.')
    } catch (error) {
      console.error('Error sending buy request:', error)
      toast.error('Failed to send purchase request. Please try again.')
    }
  }

  const handleFavoriteClick = async (e: React.MouseEvent, template: Template) => {
    e.stopPropagation();

    if (!user) {
      toast.error('Please sign in to add favorites')
      return
    }

    try {
      const isAdded = await toggleFavorite(user.uid, template.id);

      // Update local state immediately for instant UI feedback
      if (isAdded) {
        setUserFavorites(prev => [...prev, template.id]);
        toast.success('Added to favorites!')
      } else {
        setUserFavorites(prev => prev.filter(id => id !== template.id));
        toast.success('Removed from favorites!')
      }

      // Refresh user data in background
      refreshUserData();
    } catch (error) {
      console.error('Error toggling favorite:', error)
      toast.error('Failed to update favorites. Please try again.')
    }
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Templates</h1>
          <p className="text-muted-foreground">Loading templates...</p>
        </div>
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
          {[...Array(8)].map((_, i) => (
            <Card key={i} className="overflow-hidden">
              <div className="aspect-video bg-muted animate-pulse" />
              <CardHeader>
                <div className="h-4 bg-muted animate-pulse rounded" />
                <div className="h-3 bg-muted animate-pulse rounded w-3/4" />
              </CardHeader>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-8 px-4 sm:px-6 lg:px-8 py-4 sm:py-6 lg:py-8">
      {/* Enhanced Header */}
      <div className="text-center space-y-4 py-8">
        <div className="space-y-2">
          <Badge variant="secondary" className="mb-4">
            <Award className="h-4 w-4 mr-2" />
            50+ Premium Templates
          </Badge>
          <h1 className="text-3xl sm:text-4xl lg:text-5xl font-bold tracking-tight bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            Premium Templates
          </h1>
          <p className="text-base sm:text-lg lg:text-xl text-muted-foreground max-w-2xl mx-auto">
            Choose from our collection of professionally designed, responsive templates.
            Each template is crafted with modern design principles and best practices.
          </p>
        </div>

        {/* Stats */}
        <div className="flex justify-center items-center gap-6 sm:gap-8 pt-4">
          <div className="text-center">
            <div className="text-xl sm:text-2xl font-bold text-blue-600">50+</div>
            <div className="text-xs sm:text-sm text-muted-foreground">Templates</div>
          </div>
          <div className="text-center">
            <div className="text-xl sm:text-2xl font-bold text-green-600">4.9★</div>
            <div className="text-xs sm:text-sm text-muted-foreground">Rating</div>
          </div>
          <div className="text-center">
            <div className="text-xl sm:text-2xl font-bold text-purple-600">10K+</div>
            <div className="text-xs sm:text-sm text-muted-foreground">Downloads</div>
          </div>
        </div>
      </div>

      {/* Enhanced Search and Filter */}
      <div className="bg-muted/30 rounded-2xl p-4 sm:p-6 space-y-4">
        <div className="flex flex-col lg:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search templates by name or description..."
              className="pl-10 h-10 sm:h-12 text-sm sm:text-lg"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <Select value={sortBy} onValueChange={setSortBy}>
            <SelectTrigger className="w-full lg:w-48 h-10 sm:h-12">
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="title">Sort by Title</SelectItem>
              <SelectItem value="price-low">Price: Low to High</SelectItem>
              <SelectItem value="price-high">Price: High to Low</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Category Filter */}
        <div className="space-y-2">
          <h3 className="text-sm font-medium text-muted-foreground">Categories</h3>
          <div className="flex gap-2 flex-wrap">
            {categories.map((category) => (
              <Button
                key={category}
                variant={category === selectedCategory ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedCategory(category)}
                className="rounded-full text-xs sm:text-sm"
              >
                {category}
                {category !== "All" && (
                  <Badge variant="secondary" className="ml-2 text-xs">
                    {templates.filter(t => t.category === category).length}
                  </Badge>
                )}
              </Button>
            ))}
          </div>
        </div>

        {/* Results Count */}
        <div className="flex justify-between items-center text-sm text-muted-foreground">
          <span>
            Showing {filteredTemplates.length} of {templates.length} templates
          </span>
          {searchTerm && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setSearchTerm("")}
              className="text-xs"
            >
              Clear search
            </Button>
          )}
        </div>
      </div>

      {/* Enhanced Templates Grid */}
      <div className="grid gap-6 sm:gap-8 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
        {filteredTemplates.map((template) => (
          <Card key={template.id} className="group overflow-hidden hover:shadow-2xl transition-all duration-300 border-0 shadow-lg cursor-pointer">
            {/* Image Container with Overlay */}
            <div className="aspect-video bg-gradient-to-br from-blue-50 to-purple-50 relative overflow-hidden">
              {template.imageUrl ? (
                <img
                  src={template.imageUrl}
                  alt={template.title}
                  className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                />
              ) : (
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-center space-y-2">
                    <div className="w-12 sm:w-16 h-12 sm:h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto">
                      <Zap className="h-6 w-6 sm:h-8 sm:w-8 text-blue-600" />
                    </div>
                    <p className="text-muted-foreground font-medium text-sm sm:text-base">Template Preview</p>
                  </div>
                </div>
              )}

              {/* Overlay with Quick Actions */}
              <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center gap-2 sm:gap-3">
                <Button
                  size="sm"
                  variant="secondary"
                  className="bg-white/90 hover:bg-white text-black border-0 text-xs sm:text-sm cursor-pointer"
                  onClick={() => window.open('/templates', '_blank')}
                >
                  <Eye className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
                  Preview
                </Button>
              </div>

              {/* Category Badge */}
              <div className="absolute top-2 sm:top-3 left-2 sm:left-3">
                <Badge variant="secondary" className="bg-white/90 text-black border-0 text-xs">
                  {template.category}
                </Badge>
              </div>

              {/* Favorite Button */}
              <div className="absolute top-2 sm:top-3 right-2 sm:right-3">
                <Button
                  size="sm"
                  variant="secondary"
                  className="w-6 h-6 sm:w-8 sm:h-8 p-0 bg-white/90 hover:bg-white text-black border-0 rounded-full cursor-pointer"
                  onClick={(e) => handleFavoriteClick(e, template)}
                >
                  <Heart className={`h-3 w-3 sm:h-4 sm:w-4 ${userFavorites.includes(template.id) ? 'fill-red-500 text-red-500' : ''}`} />
                </Button>
              </div>
            </div>

            {/* Card Content */}
            <CardHeader className="pb-3">
              <div className="space-y-2">
                <div className="flex items-start justify-between">
                  <CardTitle className="text-base sm:text-xl font-bold group-hover:text-blue-600 transition-colors line-clamp-2">
                    {template.title}
                  </CardTitle>
                  <div className="flex items-center gap-1 text-yellow-500 flex-shrink-0 ml-2">
                    <Star className="h-3 w-3 sm:h-4 sm:w-4 fill-current" />
                    <span className="text-xs sm:text-sm font-medium">{template.rating || '4.9'}</span>
                  </div>
                </div>
                <CardDescription className="text-xs sm:text-sm leading-relaxed line-clamp-2">
                  {template.description}
                </CardDescription>
              </div>
            </CardHeader>

            <CardContent className="pt-0">
              {/* Features */}
              <div className="flex flex-wrap gap-1 mb-3 sm:mb-4">
                <Badge variant="outline" className="text-xs">Responsive</Badge>
                <Badge variant="outline" className="text-xs">Modern</Badge>
                <Badge variant="outline" className="text-xs">Fast</Badge>
              </div>

              {/* Price and Actions */}
              <div className="space-y-3 sm:space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-1">
                    {template.minPrice && template.maxPrice ? (
                      <span className="text-2xl sm:text-3xl font-bold text-green-600">
                        ₹{template.minPrice} - ₹{template.maxPrice}
                      </span>
                    ) : (
                      <span className="text-2xl sm:text-3xl font-bold text-green-600">₹{template.price}</span>
                    )}
                  </div>
                  <div className="text-right">
                    <div className="text-xs text-muted-foreground line-through">₹{Math.round(template.price * 1.5)}</div>
                    <div className="text-xs text-green-600 font-medium">33% OFF</div>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="space-y-2">
                  <div className="grid grid-cols-2 gap-2">
                    <Button
                      variant="outline"
                      className="w-full group/btn hover:bg-blue-50 hover:border-blue-200 hover:text-blue-700 transition-all text-xs sm:text-sm cursor-pointer"
                      onClick={() => window.open('/customize', '_blank')}
                    >
                      <Eye className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2 group-hover/btn:scale-110 transition-transform" />
                      <span className="hidden sm:inline">Preview</span>
                      <span className="sm:hidden">View</span>
                    </Button>

                    <Button
                      className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white border-0 font-medium group/btn text-xs sm:text-sm cursor-pointer"
                      onClick={() => handleBuyRequest(template)}
                    >
                      <MessageCircle className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2 group-hover/btn:scale-110 transition-transform" />
                      <span className="hidden sm:inline">Contact to Buy</span>
                      <span className="sm:hidden">Buy</span>
                    </Button>
                  </div>

                  <Button
                    variant="outline"
                    className="w-full group/btn hover:bg-green-50 hover:border-green-200 hover:text-green-700 transition-all text-xs sm:text-sm cursor-pointer"
                    onClick={() => handleContactRequest(template)}
                  >
                    <MessageCircle className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2 group-hover/btn:scale-110 transition-transform" />
                    <span className="hidden sm:inline">Contact for Info</span>
                    <span className="sm:hidden">Contact</span>
                  </Button>
                </div>


              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredTemplates.length === 0 && !loading && (
        <div className="text-center py-12">
          <div className="text-gray-400 mb-4">
            <Filter className="h-12 w-12 sm:h-16 sm:w-16 mx-auto" />
          </div>
          <h3 className="text-lg sm:text-xl font-semibold text-gray-900 mb-2">No templates found</h3>
          <p className="text-sm sm:text-base text-gray-600 mb-6">
            Try adjusting your search criteria or browse all templates
          </p>
          <Button onClick={() => {
            setSearchTerm('');
            setSelectedCategory('All');
            setSortBy('title');
          }}>
            Clear Filters
          </Button>
        </div>
      )}
    </div>
  )
}
