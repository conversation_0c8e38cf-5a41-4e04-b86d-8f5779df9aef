'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { ArrowLeft, Save, Star } from 'lucide-react';
import Link from 'next/link';
import { useRouter, useParams } from 'next/navigation';
import { doc, getDoc, updateDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { toast } from 'sonner';
import { Template } from '@/types';

export default function EditTemplatePage() {
  const { user, userData } = useAuth();
  const router = useRouter();
  const params = useParams();
  const templateId = params.id as string;

  const [loading, setLoading] = useState(false);
  const [fetchLoading, setFetchLoading] = useState(true);
  const [template, setTemplate] = useState<Template | null>(null);
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    category: '',
    customCategory: '',
    price: '',
    minPrice: '',
    maxPrice: '',
    features: '',
    demoUrl: '',
    downloadUrl: '',
    imageUrl: '',
    tags: '',
    featured: false
  });

  useEffect(() => {
    if (templateId) {
      fetchTemplate();
    }
  }, [templateId]);

  const fetchTemplate = async () => {
    try {
      setFetchLoading(true);
      const templateDoc = await getDoc(doc(db, 'templates', templateId));
      
      if (templateDoc.exists()) {
        const templateData = { id: templateDoc.id, ...templateDoc.data() } as Template;
        setTemplate(templateData);
        
        // Populate form data
        const isCustomCategory = !['Dashboard', 'E-commerce', 'Landing Page', 'Portfolio', 'Education', 'Blog', 'Business', 'Technology'].includes(templateData.category || '');

        setFormData({
          title: templateData.title || '',
          description: templateData.description || '',
          category: isCustomCategory ? 'Other' : templateData.category || '',
          customCategory: isCustomCategory ? templateData.category || '' : '',
          price: templateData.price?.toString() || '',
          minPrice: templateData.minPrice?.toString() || '',
          maxPrice: templateData.maxPrice?.toString() || '',
          features: Array.isArray(templateData.keyFeatures)
            ? templateData.keyFeatures.join('\n')
            : templateData.keyFeatures || '',
          demoUrl: templateData.demoUrl || templateData.previewUrl || '',
          downloadUrl: templateData.downloadUrl || '',
          imageUrl: templateData.imageUrl || '',
          tags: Array.isArray(templateData.tags)
            ? templateData.tags.join(', ')
            : '',
          featured: templateData.featured || false
        });
      } else {
        toast.error('Template not found');
        router.push('/admin');
      }
    } catch (error) {
      console.error('Error fetching template:', error);
      toast.error('Failed to load template');
      router.push('/admin');
    } finally {
      setFetchLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!template) return;

    setLoading(true);

    try {
      // Prepare template data - only include fields that have values
      const templateData: any = {
        updatedAt: new Date()
      };

      // Only update fields that have values
      if (formData.title && formData.title.trim()) {
        templateData.title = formData.title.trim();
      }

      if (formData.description && formData.description.trim()) {
        templateData.description = formData.description.trim();
      }

      if (formData.category) {
        const finalCategory = formData.category === 'Other' ? formData.customCategory.trim() : formData.category;
        if (finalCategory) {
          templateData.category = finalCategory;
        }
      }

      if (formData.price !== undefined && formData.price !== '') {
        const priceValue = parseFloat(formData.price);
        if (!isNaN(priceValue)) {
          templateData.price = priceValue;
          templateData.premium = priceValue > 0;
          templateData.free = priceValue === 0;
        }
      }

      if (formData.imageUrl && formData.imageUrl.trim()) {
        templateData.imageUrl = formData.imageUrl.trim();
      }

      if (formData.tags !== undefined) {
        templateData.tags = formData.tags ? formData.tags.split(',').map(tag => tag.trim()).filter(Boolean) : [];
      }

      // Featured status can be updated even if false
      templateData.featured = formData.featured;

      // Only add optional fields if they have values and are valid numbers
      if (formData.minPrice && formData.minPrice.trim() && !isNaN(parseFloat(formData.minPrice))) {
        templateData.minPrice = parseFloat(formData.minPrice);
      }
      if (formData.maxPrice && formData.maxPrice.trim() && !isNaN(parseFloat(formData.maxPrice))) {
        templateData.maxPrice = parseFloat(formData.maxPrice);
      }
      if (formData.demoUrl && formData.demoUrl.trim()) {
        templateData.demoUrl = formData.demoUrl.trim();
        templateData.previewUrl = formData.demoUrl.trim();
      }
      if (formData.downloadUrl && formData.downloadUrl.trim()) {
        templateData.downloadUrl = formData.downloadUrl.trim();
      }
      if (formData.features && formData.features.trim()) {
        templateData.keyFeatures = formData.features.trim();
      }

      // Update template in Firebase
      await updateDoc(doc(db, 'templates', templateId), templateData);

      toast.success('Template updated successfully!');

      // Redirect to admin page after a short delay
      setTimeout(() => {
        router.push('/admin');
      }, 1500);

    } catch (error) {
      console.error('Error updating template:', error);
      toast.error('Failed to update template. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: field === 'featured' ? (typeof value === 'boolean' ? value : value === 'true') : value
    }));
  };

  if (!user || userData?.role !== 'admin') {
    return (
      <div className="container mx-auto px-4 py-20 text-center">
        <h1 className="text-2xl font-bold mb-4">Access Denied</h1>
        <p className="text-gray-600 mb-4">You need admin privileges to access this page.</p>
        <Button asChild>
          <Link href="/">Go to Home</Link>
        </Button>
      </div>
    );
  }

  if (fetchLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading template...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-4 mb-4">
            <Button asChild variant="outline" size="sm">
              <Link href="/admin">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Admin
              </Link>
            </Button>
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Edit Template
          </h1>
          <p className="text-gray-600">
            Update template information and settings
          </p>
        </div>

        <div className="max-w-6xl mx-auto">
          <Card className="shadow-lg border-0">
            <CardHeader className="bg-gradient-to-r from-blue-50 to-purple-50 border-b">
              <CardTitle className="flex items-center text-2xl">
                <Save className="mr-3 h-6 w-6 text-blue-600" />
                Template Details
              </CardTitle>
              <CardDescription className="text-base">
                Update any information for your template. Only modify the fields you want to change.
              </CardDescription>
            </CardHeader>
            <CardContent className="p-8">
              <form onSubmit={handleSubmit} className="space-y-8">
                {/* Basic Information Section */}
                <div className="space-y-6">
                  <h3 className="text-lg font-semibold text-gray-900 border-b pb-2">Basic Information</h3>
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="title" className="text-sm font-medium">Template Title</Label>
                      <Input
                        id="title"
                        value={formData.title}
                        onChange={(e) => handleInputChange('title', e.target.value)}
                        placeholder="e.g., SaaS Dashboard Pro"
                        className="h-11"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="category" className="text-sm font-medium">Category</Label>
                      <Select value={formData.category} onValueChange={(value) => handleInputChange('category', value)}>
                        <SelectTrigger className="h-11">
                          <SelectValue placeholder="Select category" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Dashboard">Dashboard</SelectItem>
                          <SelectItem value="E-commerce">E-commerce</SelectItem>
                          <SelectItem value="Landing Page">Landing Page</SelectItem>
                          <SelectItem value="Portfolio">Portfolio</SelectItem>
                          <SelectItem value="Education">Education</SelectItem>
                          <SelectItem value="Blog">Blog</SelectItem>
                          <SelectItem value="Business">Business</SelectItem>
                          <SelectItem value="Technology">Technology</SelectItem>
                          <SelectItem value="Other">Other (Custom)</SelectItem>
                        </SelectContent>
                      </Select>
                      {formData.category === 'Other' && (
                        <Input
                          placeholder="Enter custom category"
                          value={formData.customCategory}
                          onChange={(e) => handleInputChange('customCategory', e.target.value)}
                          className="h-11 mt-2"
                        />
                      )}
                    </div>
                  </div>
                </div>

                {/* Description Section */}
                <div className="space-y-6">
                  <h3 className="text-lg font-semibold text-gray-900 border-b pb-2">Description & Details</h3>
                  <div className="space-y-2">
                    <Label htmlFor="description" className="text-sm font-medium">Description</Label>
                    <Textarea
                      id="description"
                      value={formData.description}
                      onChange={(e) => handleInputChange('description', e.target.value)}
                      placeholder="Provide a detailed description of your template, its features, and what makes it unique..."
                      rows={4}
                      className="resize-none"
                    />
                  </div>
                </div>

                {/* Media & Links Section */}
                <div className="space-y-6">
                  <h3 className="text-lg font-semibold text-gray-900 border-b pb-2">Media & Links</h3>
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="imageUrl" className="text-sm font-medium">Image URL</Label>
                      <Input
                        id="imageUrl"
                        type="url"
                        value={formData.imageUrl}
                        onChange={(e) => handleInputChange('imageUrl', e.target.value)}
                        placeholder="https://images.unsplash.com/photo-..."
                        className="h-11"
                      />
                      <p className="text-xs text-gray-500">Use high-quality images from Unsplash or similar services</p>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="demoUrl" className="text-sm font-medium">Demo URL</Label>
                      <Input
                        id="demoUrl"
                        type="url"
                        value={formData.demoUrl}
                        onChange={(e) => handleInputChange('demoUrl', e.target.value)}
                        placeholder="https://demo.example.com"
                        className="h-11"
                      />
                    </div>
                  </div>
                </div>

                {/* Pricing & Features Section */}
                <div className="space-y-6">
                  <h3 className="text-lg font-semibold text-gray-900 border-b pb-2">Pricing & Features</h3>
                  <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="price" className="text-sm font-medium">Fixed Price (₹)</Label>
                      <Input
                        id="price"
                        type="number"
                        value={formData.price}
                        onChange={(e) => handleInputChange('price', e.target.value)}
                        placeholder="2499"
                        min="0"
                        className="h-11"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="minPrice" className="text-sm font-medium">Min Price (₹)</Label>
                      <Input
                        id="minPrice"
                        type="number"
                        value={formData.minPrice}
                        onChange={(e) => handleInputChange('minPrice', e.target.value)}
                        placeholder="1999"
                        min="0"
                        className="h-11"
                      />
                      <p className="text-xs text-gray-500">Optional: For price ranges</p>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="maxPrice" className="text-sm font-medium">Max Price (₹)</Label>
                      <Input
                        id="maxPrice"
                        type="number"
                        value={formData.maxPrice}
                        onChange={(e) => handleInputChange('maxPrice', e.target.value)}
                        placeholder="4999"
                        min="0"
                        className="h-11"
                      />
                      <p className="text-xs text-gray-500">Optional: For price ranges</p>
                    </div>
                  </div>
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="tags" className="text-sm font-medium">Tags</Label>
                      <Input
                        id="tags"
                        value={formData.tags}
                        onChange={(e) => handleInputChange('tags', e.target.value)}
                        placeholder="React, TypeScript, Tailwind CSS"
                        className="h-11"
                      />
                      <p className="text-xs text-gray-500">Separate tags with commas</p>
                    </div>
                    <div className="space-y-2">
                      <Label className="text-sm font-medium">Template Settings</Label>
                      <div className="flex items-center space-x-3 p-4 border rounded-lg bg-gradient-to-r from-yellow-50 to-orange-50">
                        <Star className="h-5 w-5 text-yellow-500" />
                        <div className="flex-1">
                          <Label htmlFor="featured" className="text-sm font-medium text-gray-900">
                            Featured Template
                          </Label>
                          <p className="text-xs text-gray-600">Show this template on the home page</p>
                        </div>
                        <Switch
                          id="featured"
                          checked={formData.featured}
                          onCheckedChange={(checked) => handleInputChange('featured', checked)}
                        />
                      </div>
                    </div>
                  </div>
                </div>

                {/* Features & Download Section */}
                <div className="space-y-6">
                  <h3 className="text-lg font-semibold text-gray-900 border-b pb-2">Features & Download</h3>
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="features" className="text-sm font-medium">Features (one per line)</Label>
                      <Textarea
                        id="features"
                        value={formData.features}
                        onChange={(e) => handleInputChange('features', e.target.value)}
                        placeholder="Responsive Design&#10;Dark Mode Support&#10;Admin Dashboard&#10;User Authentication&#10;Modern UI Components"
                        rows={6}
                        className="resize-none"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="downloadUrl" className="text-sm font-medium">Download URL</Label>
                      <Input
                        id="downloadUrl"
                        type="url"
                        value={formData.downloadUrl}
                        onChange={(e) => handleInputChange('downloadUrl', e.target.value)}
                        placeholder="https://files.example.com/template.zip"
                        className="h-11"
                      />
                      <p className="text-xs text-gray-500">Direct link to downloadable template files</p>
                    </div>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex items-center justify-between pt-8 border-t">
                  <Button type="button" variant="outline" size="lg" asChild>
                    <Link href="/admin">
                      Cancel
                    </Link>
                  </Button>
                  <Button type="submit" disabled={loading} size="lg" className="px-8">
                    {loading ? (
                      <>
                        <Save className="mr-2 h-4 w-4 animate-spin" />
                        Updating Template...
                      </>
                    ) : (
                      <>
                        <Save className="mr-2 h-4 w-4" />
                        Update Template
                      </>
                    )}
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
