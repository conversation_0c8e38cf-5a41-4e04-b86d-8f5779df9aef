'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import {
  Users,
  FileText,
  ShoppingCart,
  DollarSign,
  Plus,
  Eye,
  Settings,
  BarChart3,
  Database,
  MessageSquare,
  Palette,
  Download,
  Phone,
  CreditCard,
  Filter,
  Bot
} from 'lucide-react';
import TemplatesTab from '@/components/admin/TemplatesTab';
import StatusDropdown from '@/components/admin/StatusDropdown';
import ChatbotTab from '@/components/admin/ChatbotTab';
import CareersTab from '@/components/admin/CareersTab';
import InternshipsTab from '@/components/admin/InternshipsTab';
import Link from 'next/link';
import {
  getDashboardStats,
  getCustomRequests,
  getAllUsers,
  updateCustomRequestStatus,
  subscribeToCustomRequests,
  getContactMessages,
  updateContactMessageStatus,
  subscribeToContactMessages
} from '@/lib/firebaseServices';
import { CustomRequest, User, ContactMessage } from '@/types';

interface DashboardStats {
  totalUsers: number;
  totalTemplates: number;
  totalRequests: number;
  pendingRequests: number;
  totalSales: number;
  customizations: number;
}

export default function AdminDashboard() {
  const { user, userData } = useAuth();
  const [stats, setStats] = useState<DashboardStats>({
    totalUsers: 0,
    totalTemplates: 0,
    totalRequests: 0,
    pendingRequests: 0,
    totalSales: 0,
    customizations: 0
  });
  const [customRequests, setCustomRequests] = useState<CustomRequest[]>([]);
  const [contactMessages, setContactMessages] = useState<ContactMessage[]>([]);
  const [recentUsers, setRecentUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [activeTab, setActiveTab] = useState('templates');
  const [statusFilter, setStatusFilter] = useState('all');
  const [selectedRequest, setSelectedRequest] = useState<any>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // Fetch dashboard stats
        const dashboardStats = await getDashboardStats();
        setStats(dashboardStats);

        // Fetch custom requests
        const requests = await getCustomRequests();
        setCustomRequests(requests.slice(0, 5)); // Show only recent 5

        // Fetch recent users
        const users = await getAllUsers();
        setRecentUsers(users.slice(0, 5)); // Show only recent 5

        // Fetch contact messages
        const messages = await getContactMessages();
        setContactMessages(messages.slice(0, 10)); // Show only recent 10

      } catch (error: any) {
        console.error('Error fetching admin data:', error);
        setError('Failed to load dashboard data');
      } finally {
        setLoading(false);
      }
    };

    if (user && userData?.role === 'admin') {
      fetchData();

      // Set up real-time listeners
      const unsubscribeRequests = subscribeToCustomRequests((requests) => {
        setCustomRequests(requests.slice(0, 5));
      });

      const unsubscribeMessages = subscribeToContactMessages((messages) => {
        setContactMessages(messages.slice(0, 10));
      });

      return () => {
        unsubscribeRequests();
        unsubscribeMessages();
      };
    }
  }, [user, userData]);

  const handleUpdateRequestStatus = async (requestId: string, status: CustomRequest['status']) => {
    try {
      await updateCustomRequestStatus(requestId, status);
      // The real-time listener will update the UI automatically
    } catch (error: any) {
      console.error('Error updating request status:', error);
      setError('Failed to update request status');
    }
  };

  const handleUpdateMessageStatus = async (messageId: string, status: ContactMessage['status']) => {
    try {
      await updateContactMessageStatus(messageId, status);
      // The real-time listener will update the UI automatically
    } catch (error: any) {
      console.error('Error updating message status:', error);
      setError('Failed to update message status');
    }
  };

  if (!user || userData?.role !== 'admin') {
    return (
      <div className="container mx-auto px-4 py-20 text-center">
        <h1 className="text-2xl font-bold mb-4">Access Denied</h1>
        <p className="text-gray-600 mb-4">You need admin privileges to access this page.</p>
        <Button asChild>
          <Link href="/">Go to Home</Link>
        </Button>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                Admin Dashboard
              </h1>
              <p className="text-gray-600">
                Manage your marketplace and monitor performance
              </p>
            </div>

          </div>
        </div>

      {/* Loading State */}
      {loading && (
        <div className="flex justify-center items-center py-12">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading dashboard data...</p>
          </div>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="mb-8 p-4 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-red-800">{error}</p>
        </div>
      )}

        {/* Stats Cards */}
        {!loading && (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
            {/* Templates Card */}
            <Card className="bg-gradient-to-r from-blue-500 to-blue-600 text-white border-0">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-blue-100 text-sm font-medium mb-1">Templates</p>
                    <p className="text-3xl font-bold">{stats.totalTemplates}</p>
                    <p className="text-blue-100 text-xs">Available templates</p>
                  </div>
                  <div className="p-3 bg-white/20 rounded-lg">
                    <FileText className="h-8 w-8 text-white" />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Contact Requests Card */}
            <Card className="bg-gradient-to-r from-green-500 to-green-600 text-white border-0">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-green-100 text-sm font-medium mb-1">Contact Requests</p>
                    <p className="text-3xl font-bold">{stats.totalRequests}</p>
                    <p className="text-green-100 text-xs">Customer inquiries</p>
                  </div>
                  <div className="p-3 bg-white/20 rounded-lg">
                    <MessageSquare className="h-8 w-8 text-white" />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Total Sales Card */}
            <Card className="bg-gradient-to-r from-purple-500 to-purple-600 text-white border-0">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-purple-100 text-sm font-medium mb-1">Total Sales</p>
                    <p className="text-3xl font-bold">{stats.pendingRequests}</p>
                    <p className="text-purple-100 text-xs">No revenue</p>
                  </div>
                  <div className="p-3 bg-white/20 rounded-lg">
                    <DollarSign className="h-8 w-8 text-white" />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Customizations Card */}
            <Card className="bg-gradient-to-r from-orange-500 to-orange-600 text-white border-0">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-orange-100 text-sm font-medium mb-1">Customizations</p>
                    <p className="text-3xl font-bold">{stats.customizations}</p>
                    <p className="text-orange-100 text-xs">Total customizations</p>
                  </div>
                  <div className="p-3 bg-white/20 rounded-lg">
                    <Palette className="h-8 w-8 text-white" />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Users Card */}
            <Card className="bg-gradient-to-r from-cyan-500 to-cyan-600 text-white border-0">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-cyan-100 text-sm font-medium mb-1">Users</p>
                    <p className="text-3xl font-bold">{stats.totalUsers}</p>
                    <p className="text-cyan-100 text-xs">Site visitors</p>
                  </div>
                  <div className="p-3 bg-white/20 rounded-lg">
                    <Users className="h-8 w-8 text-white" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Navigation Tabs */}
        <div className="mb-8">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              <button
                onClick={() => setActiveTab('templates')}
                className={`border-b-2 py-2 px-1 text-sm font-medium cursor-pointer ${
                  activeTab === 'templates'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Templates Management
              </button>
              <button
                onClick={() => setActiveTab('requests')}
                className={`border-b-2 py-2 px-1 text-sm font-medium cursor-pointer ${
                  activeTab === 'requests'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Contact Requests
              </button>
              <button
                onClick={() => setActiveTab('purchases')}
                className={`border-b-2 py-2 px-1 text-sm font-medium cursor-pointer ${
                  activeTab === 'purchases'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Purchase Requests
              </button>
              <button
                onClick={() => setActiveTab('customizations')}
                className={`border-b-2 py-2 px-1 text-sm font-medium cursor-pointer ${
                  activeTab === 'customizations'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Customizations
              </button>
              <button
                onClick={() => setActiveTab('chatbot')}
                className={`border-b-2 py-2 px-1 text-sm font-medium cursor-pointer ${
                  activeTab === 'chatbot'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Chatbot Q&A
              </button>
              <button
                onClick={() => setActiveTab('careers')}
                className={`border-b-2 py-2 px-1 text-sm font-medium cursor-pointer ${
                  activeTab === 'careers'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Careers
              </button>
              <button
                onClick={() => setActiveTab('internships')}
                className={`border-b-2 py-2 px-1 text-sm font-medium cursor-pointer ${
                  activeTab === 'internships'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Internships
              </button>
            </nav>
          </div>
        </div>

        {!loading && (
          <div className="grid lg:grid-cols-3 gap-8">
            {/* Content based on active tab */}
            <div className="lg:col-span-2">
              {activeTab === 'templates' && (
                <TemplatesTab onRefresh={() => {
                  // Refresh dashboard stats if needed
                  console.log('Templates updated');
                }} />
              )}

              {activeTab === 'requests' && (
                <Card>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div>
                        <CardTitle>Contact Requests</CardTitle>
                        <CardDescription>
                          Manage customer inquiries and requests
                        </CardDescription>
                      </div>
                      <div className="flex items-center gap-2">
                        <Filter className="h-4 w-4 text-gray-500" />
                        <Select value={statusFilter} onValueChange={setStatusFilter}>
                          <SelectTrigger className="w-40">
                            <SelectValue placeholder="Filter by status" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="all">All Status</SelectItem>
                            <SelectItem value="pending">Pending</SelectItem>
                            <SelectItem value="responded">Responded</SelectItem>
                            <SelectItem value="in-progress">In Progress</SelectItem>
                            <SelectItem value="resolved">Resolved</SelectItem>
                            <SelectItem value="declined">Declined</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {contactMessages
                        .filter(msg => msg.type === 'contact')
                        .filter(msg => statusFilter === 'all' || msg.status === statusFilter)
                        .length > 0 ? (
                        contactMessages
                          .filter(msg => msg.type === 'contact')
                          .filter(msg => statusFilter === 'all' || msg.status === statusFilter)
                          .map((message) => (
                          <div
                            key={message.id}
                            className="p-4 border rounded-lg cursor-pointer hover:bg-gray-50 transition-colors"
                            onClick={() => setSelectedRequest(message)}
                          >
                            <div className="flex items-start justify-between">
                              <div className="flex-1">
                                <div className="flex items-center gap-2 mb-2">
                                  <h4 className="font-medium text-gray-900">{message.userName}</h4>
                                  <Badge variant="secondary" className="text-xs">
                                    Contact Inquiry
                                  </Badge>
                                </div>
                                <p className="text-sm text-gray-600">{message.userEmail}</p>
                                {message.userPhone && (
                                  <p className="text-sm text-gray-600">{message.userPhone}</p>
                                )}
                                <p className="text-sm font-medium text-gray-800 mt-1">{message.subject}</p>
                                <p className="text-sm text-gray-500 mt-2 line-clamp-2">{message.message}</p>
                                {message.templateTitle && (
                                  <p className="text-xs text-blue-600 mt-1">Template: {message.templateTitle}</p>
                                )}
                                <p className="text-xs text-gray-400 mt-2">
                                  {new Date(message.createdAt.seconds ? message.createdAt.seconds * 1000 : message.createdAt).toLocaleDateString()}
                                </p>
                              </div>
                              <div className="flex items-center ml-4" onClick={(e) => e.stopPropagation()}>
                                <StatusDropdown
                                  currentStatus={message.status}
                                  onStatusChange={(status) => handleUpdateMessageStatus(message.id, status)}
                                  type="contact-message"
                                />
                              </div>
                            </div>
                          </div>
                        ))
                      ) : (
                        <div className="text-center py-8">
                          <MessageSquare className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                          <h3 className="text-lg font-semibold text-gray-900 mb-2">No contact requests</h3>
                          <p className="text-gray-600">
                            Contact requests from users will appear here.
                          </p>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              )}

              {activeTab === 'purchases' && (
                <Card>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div>
                        <CardTitle>Purchase Requests</CardTitle>
                        <CardDescription>
                          Manage template purchase requests from customers
                        </CardDescription>
                      </div>
                      <div className="flex items-center gap-2">
                        <Filter className="h-4 w-4 text-gray-500" />
                        <Select value={statusFilter} onValueChange={setStatusFilter}>
                          <SelectTrigger className="w-40">
                            <SelectValue placeholder="Filter by status" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="all">All Status</SelectItem>
                            <SelectItem value="pending">Pending</SelectItem>
                            <SelectItem value="confirmed">Confirmed</SelectItem>
                            <SelectItem value="approved">Order Approved</SelectItem>
                            <SelectItem value="completed">Completed</SelectItem>
                            <SelectItem value="declined">Declined</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {contactMessages
                        .filter(msg => msg.type === 'purchase-request')
                        .filter(msg => statusFilter === 'all' || msg.status === statusFilter)
                        .length > 0 ? (
                        contactMessages
                          .filter(msg => msg.type === 'purchase-request')
                          .filter(msg => statusFilter === 'all' || msg.status === statusFilter)
                          .map((message) => (
                          <div
                            key={message.id}
                            className="p-4 border rounded-lg cursor-pointer hover:bg-gray-50 transition-colors"
                            onClick={() => setSelectedRequest(message)}
                          >
                            <div className="flex items-start justify-between">
                              <div className="flex-1">
                                <div className="flex items-center gap-2 mb-2">
                                  <h4 className="font-medium text-gray-900">{message.userName}</h4>
                                  <Badge variant="default" className="text-xs">
                                    Purchase Request
                                  </Badge>
                                </div>
                                <p className="text-sm text-gray-600">{message.userEmail}</p>
                                {message.userPhone && (
                                  <p className="text-sm text-gray-600">{message.userPhone}</p>
                                )}
                                <p className="text-sm font-medium text-gray-800 mt-1">{message.subject}</p>
                                <p className="text-sm text-gray-500 mt-2 line-clamp-2">{message.message}</p>
                                {message.templateTitle && (
                                  <p className="text-xs text-blue-600 mt-1">Template: {message.templateTitle}</p>
                                )}
                                <p className="text-xs text-gray-400 mt-2">
                                  {new Date(message.createdAt.seconds ? message.createdAt.seconds * 1000 : message.createdAt).toLocaleDateString()}
                                </p>
                              </div>
                              <div className="flex items-center ml-4" onClick={(e) => e.stopPropagation()}>
                                <StatusDropdown
                                  currentStatus={message.status}
                                  onStatusChange={(status) => handleUpdateMessageStatus(message.id, status)}
                                  type="purchase-request"
                                />
                              </div>
                            </div>
                          </div>
                        ))
                      ) : (
                        <div className="text-center py-8">
                          <ShoppingCart className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                          <h3 className="text-lg font-semibold text-gray-900 mb-2">No purchase requests</h3>
                          <p className="text-gray-600">
                            Purchase requests from customers will appear here.
                          </p>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              )}

              {activeTab === 'customizations' && (
                <Card>
                  <CardHeader>
                    <CardTitle>Customization Requests</CardTitle>
                    <CardDescription>
                      Manage custom template requests
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {customRequests.length > 0 ? (
                        customRequests.map((request) => (
                          <div key={request.id} className="p-4 border rounded-lg cursor-pointer hover:bg-gray-50 transition-colors">
                            <div className="flex items-start justify-between">
                              <div className="flex-1">
                                <div className="flex items-center gap-2 mb-2">
                                  <h4 className="font-medium text-gray-900">{request.name}</h4>
                                  <Badge variant={
                                    request.status === 'pending' ? 'outline' :
                                    request.status === 'in-progress' ? 'default' :
                                    request.status === 'declined' ? 'destructive' : 'secondary'
                                  }>
                                    {request.status === 'in-progress' ? 'In Progress' : request.status}
                                  </Badge>
                                </div>
                                <p className="text-sm text-gray-600">{request.email}</p>
                                <p className="text-sm font-medium text-gray-800 mt-1">{request.projectType}</p>
                                <p className="text-sm text-gray-500 mt-2 line-clamp-2">{request.description}</p>
                                <p className="text-xs text-gray-400 mt-2">
                                  Budget: ${request.budget} | Timeline: {request.timeline}
                                </p>
                                <p className="text-xs text-gray-400">
                                  {new Date(request.createdAt.seconds ? request.createdAt.seconds * 1000 : request.createdAt).toLocaleDateString()}
                                </p>
                              </div>
                              <div className="flex items-center ml-4">
                                <StatusDropdown
                                  currentStatus={request.status}
                                  onStatusChange={(status) => handleUpdateRequestStatus(request.id, status)}
                                  type="custom-request"
                                />
                              </div>
                            </div>
                          </div>
                        ))
                      ) : (
                        <div className="text-center py-8">
                          <Palette className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                          <h3 className="text-lg font-semibold text-gray-900 mb-2">No customization requests</h3>
                          <p className="text-gray-600">
                            Custom template requests will appear here.
                          </p>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              )}

              {activeTab === 'chatbot' && (
                <ChatbotTab onRefresh={() => {
                  // Refresh dashboard stats if needed
                  console.log('Chatbot Q&As updated');
                }} />
              )}

              {activeTab === 'careers' && (
                <CareersTab onRefresh={() => {
                  // Refresh dashboard stats if needed
                  console.log('Careers updated');
                }} />
              )}

              {activeTab === 'internships' && (
                <InternshipsTab onRefresh={() => {
                  // Refresh dashboard stats if needed
                  console.log('Internships updated');
                }} />
              )}
            </div>

            {/* Quick Actions */}
            <div>
              <Card>
                <CardHeader>
                  <CardTitle>Quick Actions</CardTitle>
                  <CardDescription>
                    Common admin tasks
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <Button
                    onClick={() => setActiveTab('templates')}
                    variant={activeTab === 'templates' ? 'default' : 'outline'}
                    className="w-full justify-start"
                  >
                    <FileText className="mr-2 h-4 w-4" />
                    Templates
                  </Button>

                  <Button
                    onClick={() => setActiveTab('requests')}
                    variant={activeTab === 'requests' ? 'default' : 'outline'}
                    className="w-full justify-start"
                  >
                    <MessageSquare className="mr-2 h-4 w-4" />
                    Contact Requests
                  </Button>

                  <Button asChild variant="outline" className="w-full justify-start">
                    <Link href="/admin/custom-requests">
                      <Palette className="mr-2 h-4 w-4" />
                      Customize Requests
                    </Link>
                  </Button>

                  <Button asChild variant="outline" className="w-full justify-start">
                    <Link href="/admin/purchase-requests">
                      <CreditCard className="mr-2 h-4 w-4" />
                      Purchase Requests
                    </Link>
                  </Button>
                </CardContent>
              </Card>

              {/* System Status */}
              <Card className="mt-6">
                <CardHeader>
                  <CardTitle>System Status</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <Database className="w-4 h-4 mr-2 text-green-600" />
                        <span className="text-sm text-gray-600">Firebase</span>
                      </div>
                      <Badge variant="default" className="bg-green-500">Connected</Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <Database className="w-4 h-4 mr-2 text-green-600" />
                        <span className="text-sm text-gray-600">Firestore</span>
                      </div>
                      <Badge variant="default" className="bg-green-500">Active</Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <Users className="w-4 h-4 mr-2 text-green-600" />
                        <span className="text-sm text-gray-600">Authentication</span>
                      </div>
                      <Badge variant="default" className="bg-green-500">Working</Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <FileText className="w-4 h-4 mr-2 text-gray-600" />
                        <span className="text-sm text-gray-600">Total Templates</span>
                      </div>
                      <span className="text-sm text-gray-900">{stats.totalTemplates}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        )}

        {/* Detailed Request View Dialog */}
        {selectedRequest && (
          <Dialog open={!!selectedRequest} onOpenChange={() => setSelectedRequest(null)}>
            <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>Request Details</DialogTitle>
                <DialogDescription>
                  Complete information about this request
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-6">
                {/* User Information */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold border-b pb-2">User Information</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-gray-600">Name</label>
                      <p className="text-sm text-gray-900">{selectedRequest.userName || selectedRequest.title}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-600">Email</label>
                      <p className="text-sm text-gray-900">{selectedRequest.userEmail}</p>
                    </div>
                    {selectedRequest.userPhone && (
                      <div>
                        <label className="text-sm font-medium text-gray-600">Phone</label>
                        <p className="text-sm text-gray-900">{selectedRequest.userPhone}</p>
                      </div>
                    )}
                    <div>
                      <label className="text-sm font-medium text-gray-600">Status</label>
                      <div className="mt-1">
                        <StatusDropdown
                          currentStatus={selectedRequest.status}
                          onStatusChange={(status) => {
                            if (selectedRequest.type) {
                              handleUpdateMessageStatus(selectedRequest.id, status);
                            } else {
                              handleUpdateRequestStatus(selectedRequest.id, status);
                            }
                          }}
                          type={selectedRequest.type === 'purchase-request' ? 'purchase-request' :
                                selectedRequest.type === 'contact' ? 'contact-message' : 'custom-request'}
                        />
                      </div>
                    </div>
                  </div>
                </div>

                {/* Request Details */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold border-b pb-2">Request Details</h3>
                  <div className="space-y-4">
                    <div>
                      <label className="text-sm font-medium text-gray-600">Subject/Title</label>
                      <p className="text-sm text-gray-900">{selectedRequest.subject || selectedRequest.title}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-600">Description/Message</label>
                      <p className="text-sm text-gray-900 whitespace-pre-wrap">{selectedRequest.message || selectedRequest.description}</p>
                    </div>
                    {selectedRequest.templateTitle && (
                      <div>
                        <label className="text-sm font-medium text-gray-600">Template</label>
                        <p className="text-sm text-blue-600">{selectedRequest.templateTitle}</p>
                      </div>
                    )}
                    {selectedRequest.category && (
                      <div>
                        <label className="text-sm font-medium text-gray-600">Category</label>
                        <p className="text-sm text-gray-900">{selectedRequest.category}</p>
                      </div>
                    )}
                    {selectedRequest.budget && (
                      <div>
                        <label className="text-sm font-medium text-gray-600">Budget</label>
                        <p className="text-sm text-gray-900">₹{selectedRequest.budget}</p>
                      </div>
                    )}
                    {selectedRequest.deadline && (
                      <div>
                        <label className="text-sm font-medium text-gray-600">Deadline</label>
                        <p className="text-sm text-gray-900">
                          {new Date(selectedRequest.deadline.seconds ? selectedRequest.deadline.seconds * 1000 : selectedRequest.deadline).toLocaleDateString()}
                        </p>
                      </div>
                    )}
                  </div>
                </div>

                {/* Timestamps */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold border-b pb-2">Timeline</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-gray-600">Created At</label>
                      <p className="text-sm text-gray-900">
                        {new Date(selectedRequest.createdAt.seconds ? selectedRequest.createdAt.seconds * 1000 : selectedRequest.createdAt).toLocaleString()}
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-600">Last Updated</label>
                      <p className="text-sm text-gray-900">
                        {new Date(selectedRequest.updatedAt?.seconds ? selectedRequest.updatedAt.seconds * 1000 : selectedRequest.updatedAt || selectedRequest.createdAt).toLocaleString()}
                      </p>
                    </div>
                  </div>
                </div>

                {/* Admin Notes */}
                {selectedRequest.adminNotes && (
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold border-b pb-2">Admin Notes</h3>
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                      <p className="text-sm text-blue-800">{selectedRequest.adminNotes}</p>
                    </div>
                  </div>
                )}
              </div>
            </DialogContent>
          </Dialog>
        )}
      </div>
    </div>
  );
}
