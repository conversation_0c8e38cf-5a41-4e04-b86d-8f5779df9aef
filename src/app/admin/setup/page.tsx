'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Loader2, CheckCircle, Database, Users, Shield } from 'lucide-react';
import { addSampleData, makeUserAdmin } from '@/lib/sampleData';
import { useAuth } from '@/contexts/AuthContext';

export default function AdminSetupPage() {
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState('');
  const [adminLoading, setAdminLoading] = useState(false);
  const [adminSuccess, setAdminSuccess] = useState(false);
  const [adminError, setAdminError] = useState('');
  const [userEmail, setUserEmail] = useState('');

  const { user } = useAuth();

  const handleAddSampleData = async () => {
    setLoading(true);
    setError('');
    setSuccess(false);

    try {
      await addSampleData();
      setSuccess(true);
    } catch (error: any) {
      setError(error.message || 'Failed to add sample data');
    } finally {
      setLoading(false);
    }
  };

  const handleMakeAdmin = async () => {
    if (!userEmail.trim()) {
      setAdminError('Please enter an email address');
      return;
    }

    setAdminLoading(true);
    setAdminError('');
    setAdminSuccess(false);

    try {
      // For now, we'll use the current user's ID if they're logged in
      if (user) {
        await makeUserAdmin(user.uid);
        setAdminSuccess(true);
      } else {
        setAdminError('You must be logged in to make yourself admin');
      }
    } catch (error: any) {
      setAdminError(error.message || 'Failed to make user admin');
    } finally {
      setAdminLoading(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-2xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Admin Setup
          </h1>
          <p className="text-gray-600">
            Set up your KaleidoneX marketplace with sample data
          </p>
        </div>

        <div className="space-y-6">
          {/* Sample Data Card */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Database className="h-5 w-5 mr-2" />
                Sample Data
              </CardTitle>
              <CardDescription>
                Add sample templates and categories to get started quickly
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="text-sm text-gray-600">
                  This will add:
                  <ul className="list-disc list-inside mt-2 space-y-1">
                    <li>6 sample templates across different categories</li>
                    <li>6 template categories</li>
                    <li>Professional images from Unsplash</li>
                  </ul>
                </div>

                {error && (
                  <Alert variant="destructive">
                    <AlertDescription>{error}</AlertDescription>
                  </Alert>
                )}

                {success && (
                  <Alert>
                    <CheckCircle className="h-4 w-4" />
                    <AlertDescription>
                      Sample data added successfully! You can now browse templates on your site.
                    </AlertDescription>
                  </Alert>
                )}

                <Button 
                  onClick={handleAddSampleData} 
                  disabled={loading || success}
                  className="w-full"
                >
                  {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  {success ? 'Sample Data Added' : 'Add Sample Data'}
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Instructions Card */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Users className="h-5 w-5 mr-2" />
                Next Steps
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4 text-sm text-gray-600">
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">1. Make yourself an admin</h4>
                  <p>Go to Firebase Console → Firestore → users collection → find your user → change role to "admin"</p>
                </div>
                
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">2. Test the application</h4>
                  <p>Browse templates, test the search and filtering functionality</p>
                </div>
                
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">3. Customize templates</h4>
                  <p>Replace sample templates with your own designs and content</p>
                </div>
                
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">4. Set up payments</h4>
                  <p>Integrate Stripe or your preferred payment processor</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Firebase Setup Card */}
          <Card>
            <CardHeader>
              <CardTitle>Firebase Configuration</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3 text-sm">
                <div className="flex items-center justify-between">
                  <span>Authentication</span>
                  <span className="text-green-600 font-medium">✓ Configured</span>
                </div>
                <div className="flex items-center justify-between">
                  <span>Firestore Database</span>
                  <span className="text-green-600 font-medium">✓ Connected</span>
                </div>
                <div className="flex items-center justify-between">
                  <span>Security Rules</span>
                  <span className="text-yellow-600 font-medium">⚠ Check manually</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
